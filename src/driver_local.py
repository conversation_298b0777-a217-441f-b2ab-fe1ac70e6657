from src import utils
import src.LangGraphUtils as LangGraphUtils
from langsmith.run_helpers import get_current_run_tree
import langsmith as ls


from dotenv import load_dotenv
import os

load_dotenv()

#with ls.trace("prompts-pipeline") as rt:
print("starting")
prompts = utils.load_prompt_sequence("/Users/<USER>/Documents/GitHub/prompts-library/prompts/employee_count.yaml")
markdown = utils.load_markdown("/Users/<USER>/Documents/GitHub/prompts-library/markdowns/aktax.de.md") #utils.load_markdown_db("dornbach.de", "wget_markdown")

workflow = utils.create_workflow_state(prompts)
lg = LangGraphUtils.LangGraphUtils(model="gpt-4o-mini")
graph = lg.build_graph(prompts, workflow)

print("Running graph")
initial_state = {"markdown": markdown[0:100_000]}
state_stream = graph.stream(initial_state)

print("updates")
for update in state_stream:
    print(update)
