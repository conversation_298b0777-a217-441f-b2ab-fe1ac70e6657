- name: find_team_section
  prompt: |
    You are given a company's website in markdown.
    Identify sections that list employees. Look for headings like "Team", "Our People", "Management", "Leadership", "Staff", "Board", or similar. 
    Return only the section text if found. If no team page exists, return exactly "no_team_page". 
    
    <markdown>{markdown}</markdown>

    Respond with a JSON object like:
    {{
      "team_section": ...,   // string, the team section text. "no_team_page" if no team page exists.
    }}
    
    But don't preface it with ```json.

- name: is_team_section_complete
  prompt: |
    You are given a section of a company's website that lists employees:
    
    input: {find_team_section[team_section]}

    Decide if this section represents:
    - all employees of the company ("full")
    - only a subset, such as executives or a department ("partial")
    
    You can assume full if there is more than 10 people mentioned, and preferably have multiple categories / roles, not just management.
    
    Respond with a JSON object like:
    {{
      "team_section_complete": "full" or "partial"
    }}

    But don't preface it with ```json.

- name: count_employees
  prompt: |
    You are given markdown of a company website section. 
    
    1. Extract a list of all individual team member names mentioned, separated by commas.  Ignore headings, job titles, and company names. Only return real person names. 
    The names should be extracted from your reasoning, not by executing Python code! And do not make up any names and do not write any code for this!
    
    2. Use the Python executor tool (execute_python_code) to count the number of names in the list from step 1. Example code: len([name1, name2, name3])
    
    3. Use Python (execute_python_code) to count the length of the characters in the input (i.e. the incoming markdown), if less than 50 characters, return "Too short to be a team section" in the status.  
      Example code: len(input markdown from below)
    
    When you write Python code for (2) and (3), keep it short and never make up data or examples that could go into the final result! 

    input: {find_team_section[team_section]}

    Output:
    Respond with a JSON object like:
    {{
      "employee_names": [...]   // list of strings, each a team member name, comes from your reasoning, not from executing Python code!
      "employee_count": ...   // int, number of employees, comes from executing Python code!
      "status": ...   // string, "Too short to be a team section" or "OK"
    }}

    But don't preface it with ```json.

