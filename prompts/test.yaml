- name: test
  prompt: |
    You are given markdown of a company website section.

    Use Python (execute_python_code) to count the length of the characters in the input (i.e. the incoming markdown).
    Make sure to assign the result to a variable so it can be captured.

    Example code:
    ```python
    markdown_text = """the markdown content here"""
    length = len(markdown_text)
    print("Character count: %length%")
    ```

    input: {markdown}

    Output: the value of the variable "length".